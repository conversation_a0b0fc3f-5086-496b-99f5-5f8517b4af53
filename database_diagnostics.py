#!/usr/bin/env python3
"""
Database Diagnostics Tool for ConsultEase
Comprehensive testing of database connectivity, operations, and faculty data persistence.
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta
import traceback

# Add the central_system directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'central_system'))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('database_diagnostics.log')
    ]
)
logger = logging.getLogger(__name__)


class DatabaseDiagnostics:
    """Comprehensive database diagnostics for ConsultEase."""

    def __init__(self):
        self.issues = []
        self.test_results = {}

    def run_full_diagnostics(self):
        """Run comprehensive database diagnostics."""
        logger.info("🔍 Starting comprehensive database diagnostics...")

        # Test 1: Database Connection
        self.test_results['connection'] = self._test_database_connection()

        # Test 2: Database Schema
        self.test_results['schema'] = self._test_database_schema()

        # Test 3: Faculty Model Operations
        self.test_results['faculty_operations'] = self._test_faculty_operations()

        # Test 4: Database Session Management
        self.test_results['session_management'] = self._test_session_management()

        # Test 5: Transaction Handling
        self.test_results['transactions'] = self._test_transaction_handling()

        # Test 6: Faculty Controller Database Integration
        self.test_results['faculty_controller'] = self._test_faculty_controller_db()

        # Test 7: Real-time Update Persistence
        self.test_results['realtime_updates'] = self._test_realtime_update_persistence()

        # Test 8: Database Performance
        self.test_results['performance'] = self._test_database_performance()

        # Generate comprehensive report
        self._generate_diagnostic_report()

        return self.test_results

    def _test_database_connection(self):
        """Test database connection and configuration."""
        logger.info("🔌 Testing database connection...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            # Test basic connection
            from central_system.models.base import get_db
            from central_system.services.database_manager import get_database_manager

            # Get database manager
            db_manager = get_database_manager()
            result['details']['database_url'] = db_manager.database_url
            result['details']['pool_size'] = db_manager.pool_size
            result['details']['max_overflow'] = db_manager.max_overflow

            # Test connection
            db = get_db()

            # Simple query test
            test_query = db.execute("SELECT 1 as test").fetchone()
            if test_query and test_query[0] == 1:
                result['status'] = 'success'
                result['details']['connection_test'] = 'passed'
                logger.info("✅ Database connection successful")
            else:
                result['status'] = 'failed'
                result['errors'].append("Database query returned unexpected result")

            db.close()

            # Test database manager statistics
            stats = db_manager.get_stats()
            result['details']['stats'] = {
                'total_connections': stats.total_connections,
                'active_connections': stats.active_connections,
                'failed_connections': stats.failed_connections,
                'total_queries': stats.total_queries,
                'failed_queries': stats.failed_queries,
                'avg_query_time': stats.avg_query_time
            }

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Database connection error: {str(e)}")
            logger.error(f"❌ Database connection failed: {e}")

        return result

    def _test_database_schema(self):
        """Test database schema and table structure."""
        logger.info("📋 Testing database schema...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            from central_system.models import Faculty, get_db
            from sqlalchemy import inspect

            db = get_db()
            inspector = inspect(db.bind)

            # Check if faculty table exists
            tables = inspector.get_table_names()
            result['details']['tables'] = tables

            if 'faculty' in tables:
                # Check faculty table structure
                columns = inspector.get_columns('faculty')
                column_names = [col['name'] for col in columns]
                result['details']['faculty_columns'] = column_names

                # Check required columns
                required_columns = ['id', 'name', 'department', 'status', 'last_seen']
                missing_columns = [col for col in required_columns if col not in column_names]

                if missing_columns:
                    result['status'] = 'failed'
                    result['errors'].append(f"Missing required columns: {missing_columns}")
                else:
                    result['status'] = 'success'
                    logger.info("✅ Faculty table schema is correct")

            else:
                result['status'] = 'failed'
                result['errors'].append("Faculty table does not exist")

            db.close()

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Schema test error: {str(e)}")
            logger.error(f"❌ Schema test failed: {e}")

        return result

    def _test_faculty_operations(self):
        """Test basic faculty CRUD operations."""
        logger.info("👥 Testing faculty operations...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            from central_system.models import Faculty, get_db

            db = get_db()

            # Test reading existing faculty
            faculties = db.query(Faculty).all()
            result['details']['faculty_count'] = len(faculties)

            if faculties:
                # Test reading specific faculty
                faculty = faculties[0]
                result['details']['sample_faculty'] = {
                    'id': faculty.id,
                    'name': faculty.name,
                    'status': faculty.status,
                    'last_seen': faculty.last_seen.isoformat() if faculty.last_seen else None
                }

                # Test status update
                original_status = faculty.status
                new_status = not original_status

                faculty.status = new_status
                faculty.last_seen = datetime.now()
                db.commit()

                # Verify update
                updated_faculty = db.query(Faculty).filter(Faculty.id == faculty.id).first()
                if updated_faculty.status == new_status:
                    result['details']['status_update_test'] = 'passed'

                    # Revert status
                    faculty.status = original_status
                    db.commit()

                    result['status'] = 'success'
                    logger.info("✅ Faculty operations test passed")
                else:
                    result['status'] = 'failed'
                    result['errors'].append("Status update failed to persist")

            else:
                result['status'] = 'warning'
                result['errors'].append("No faculty records found for testing")

            db.close()

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Faculty operations error: {str(e)}")
            logger.error(f"❌ Faculty operations test failed: {e}")

        return result

    def _test_session_management(self):
        """Test database session management."""
        logger.info("🔄 Testing session management...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            from central_system.services.database_manager import get_database_manager

            db_manager = get_database_manager()

            # Test multiple sessions
            sessions = []
            for i in range(3):
                session = db_manager.get_session()
                sessions.append(session)

            result['details']['multiple_sessions'] = len(sessions)

            # Test session context manager
            with db_manager.get_session_context() as session:
                test_result = session.execute("SELECT 1").fetchone()
                if test_result:
                    result['details']['context_manager'] = 'passed'

            # Close sessions
            for session in sessions:
                session.close()

            result['status'] = 'success'
            logger.info("✅ Session management test passed")

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Session management error: {str(e)}")
            logger.error(f"❌ Session management test failed: {e}")

        return result

    def _test_transaction_handling(self):
        """Test database transaction handling."""
        logger.info("💾 Testing transaction handling...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            from central_system.models import Faculty, get_db

            db = get_db()

            # Find a faculty to test with
            faculty = db.query(Faculty).first()
            if not faculty:
                result['status'] = 'warning'
                result['errors'].append("No faculty found for transaction testing")
                db.close()
                return result

            original_status = faculty.status

            # Test successful transaction
            try:
                faculty.status = not original_status
                faculty.last_seen = datetime.now()
                db.commit()
                result['details']['commit_test'] = 'passed'
            except Exception as e:
                result['errors'].append(f"Commit failed: {str(e)}")

            # Test rollback
            try:
                faculty.status = original_status  # Change back
                db.rollback()  # Rollback the change

                # Verify rollback worked
                db.refresh(faculty)
                if faculty.status != original_status:
                    result['details']['rollback_test'] = 'passed'
                else:
                    result['details']['rollback_test'] = 'failed'

            except Exception as e:
                result['errors'].append(f"Rollback test failed: {str(e)}")

            # Restore original status
            faculty.status = original_status
            db.commit()

            db.close()

            if not result['errors']:
                result['status'] = 'success'
                logger.info("✅ Transaction handling test passed")
            else:
                result['status'] = 'failed'

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Transaction test error: {str(e)}")
            logger.error(f"❌ Transaction test failed: {e}")

        return result

    def _test_faculty_controller_db(self):
        """Test faculty controller database integration."""
        logger.info("🎛️ Testing faculty controller database integration...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            from central_system.controllers.faculty_controller import FacultyController

            # Create faculty controller
            faculty_controller = FacultyController()

            # Test getting all faculty
            faculties = faculty_controller.get_all_faculty()
            result['details']['faculty_count'] = len(faculties)

            if faculties:
                # Test updating faculty status
                faculty = faculties[0]
                original_status = faculty.status
                new_status = not original_status

                # Test update_faculty_status method
                updated_faculty = faculty_controller.update_faculty_status(faculty.id, new_status)

                if updated_faculty and updated_faculty.status == new_status:
                    result['details']['status_update'] = 'passed'

                    # Revert status
                    faculty_controller.update_faculty_status(faculty.id, original_status)

                    result['status'] = 'success'
                    logger.info("✅ Faculty controller database integration test passed")
                else:
                    result['status'] = 'failed'
                    result['errors'].append("Faculty controller status update failed")

            else:
                result['status'] = 'warning'
                result['errors'].append("No faculty found for controller testing")

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Faculty controller test error: {str(e)}")
            logger.error(f"❌ Faculty controller test failed: {e}")

        return result

    def _test_realtime_update_persistence(self):
        """Test real-time update persistence."""
        logger.info("⚡ Testing real-time update persistence...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            from central_system.controllers.faculty_controller import FacultyController
            from central_system.models import Faculty, get_db

            faculty_controller = FacultyController()

            # Test MQTT status update handling
            test_data = {
                'faculty_id': 1,
                'present': True,
                'status': 'AVAILABLE',
                'timestamp': int(time.time()),
                'ntp_sync_status': 'SYNCED'
            }

            # Simulate MQTT message handling
            topic = "consultease/faculty/1/status"

            # Get original status
            db = get_db()
            faculty = db.query(Faculty).filter(Faculty.id == 1).first()

            if faculty:
                original_status = faculty.status
                original_last_seen = faculty.last_seen

                # Test handle_faculty_status_update
                faculty_controller.handle_faculty_status_update(topic, test_data)

                # Check if update persisted
                db.refresh(faculty)

                if faculty.last_seen > original_last_seen:
                    result['details']['persistence_test'] = 'passed'
                    result['details']['last_seen_updated'] = True

                    # Check status update
                    if faculty.status == test_data['present']:
                        result['details']['status_persistence'] = 'passed'
                        result['status'] = 'success'
                        logger.info("✅ Real-time update persistence test passed")
                    else:
                        result['status'] = 'failed'
                        result['errors'].append("Status update did not persist correctly")

                else:
                    result['status'] = 'failed'
                    result['errors'].append("Last seen timestamp was not updated")

            else:
                result['status'] = 'warning'
                result['errors'].append("Faculty ID 1 not found for testing")

            db.close()

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Real-time update test error: {str(e)}")
            logger.error(f"❌ Real-time update test failed: {e}")

        return result

    def _test_database_performance(self):
        """Test database performance."""
        logger.info("⚡ Testing database performance...")

        result = {
            'status': 'unknown',
            'details': {},
            'errors': []
        }

        try:
            from central_system.models import Faculty, get_db
            import time

            db = get_db()

            # Test query performance
            start_time = time.time()
            faculties = db.query(Faculty).all()
            query_time = time.time() - start_time

            result['details']['query_time'] = query_time
            result['details']['faculty_count'] = len(faculties)

            # Test multiple rapid queries
            start_time = time.time()
            for _ in range(10):
                db.query(Faculty).count()
            rapid_queries_time = time.time() - start_time

            result['details']['rapid_queries_time'] = rapid_queries_time
            result['details']['avg_query_time'] = rapid_queries_time / 10

            # Performance assessment
            if query_time < 1.0 and rapid_queries_time < 2.0:
                result['status'] = 'success'
                logger.info("✅ Database performance test passed")
            elif query_time < 5.0:
                result['status'] = 'warning'
                result['errors'].append("Database queries are slow but acceptable")
            else:
                result['status'] = 'failed'
                result['errors'].append("Database queries are too slow")

            db.close()

        except Exception as e:
            result['status'] = 'error'
            result['errors'].append(f"Performance test error: {str(e)}")
            logger.error(f"❌ Performance test failed: {e}")

        return result

    def _generate_diagnostic_report(self):
        """Generate comprehensive diagnostic report."""
        print("\n" + "=" * 80)
        print("🔍 DATABASE DIAGNOSTICS REPORT FOR CONSULTEASE")
        print("=" * 80)
        print(f"📅 Report generated: {datetime.now().isoformat()}")
        print()

        # Test results summary
        print("📊 TEST RESULTS SUMMARY:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'success' else "⚠️" if result['status'] == 'warning' else "❌"
            print(f"  {status_icon} {test_name.replace('_', ' ').title()}: {result['status'].upper()}")

            if result['errors']:
                for error in result['errors']:
                    print(f"    - {error}")
        print()

        # Detailed findings
        print("🔍 DETAILED FINDINGS:")

        # Database connection details
        if 'connection' in self.test_results:
            conn_result = self.test_results['connection']
            if 'details' in conn_result:
                details = conn_result['details']
                print(f"  📊 Database URL: {details.get('database_url', 'Unknown')}")
                print(f"  🔗 Pool Size: {details.get('pool_size', 'Unknown')}")
                if 'stats' in details:
                    stats = details['stats']
                    print(f"  📈 Total Connections: {stats.get('total_connections', 0)}")
                    print(f"  📈 Active Connections: {stats.get('active_connections', 0)}")
                    print(f"  📈 Failed Connections: {stats.get('failed_connections', 0)}")
                    print(f"  📈 Total Queries: {stats.get('total_queries', 0)}")
                    print(f"  📈 Failed Queries: {stats.get('failed_queries', 0)}")

        # Faculty data details
        if 'faculty_operations' in self.test_results:
            faculty_result = self.test_results['faculty_operations']
            if 'details' in faculty_result:
                details = faculty_result['details']
                print(f"  👥 Faculty Count: {details.get('faculty_count', 0)}")
                if 'sample_faculty' in details:
                    sample = details['sample_faculty']
                    print(f"  👤 Sample Faculty: {sample.get('name', 'Unknown')} (Status: {sample.get('status', 'Unknown')})")

        # Performance details
        if 'performance' in self.test_results:
            perf_result = self.test_results['performance']
            if 'details' in perf_result:
                details = perf_result['details']
                print(f"  ⚡ Query Time: {details.get('query_time', 0):.3f}s")
                print(f"  ⚡ Avg Query Time: {details.get('avg_query_time', 0):.3f}s")
        print()

        # Issues and recommendations
        all_errors = []
        for result in self.test_results.values():
            all_errors.extend(result.get('errors', []))

        if all_errors:
            print("❌ ISSUES IDENTIFIED:")
            for i, error in enumerate(all_errors, 1):
                print(f"  {i}. {error}")
            print()

        print("💡 RECOMMENDATIONS:")

        # Check for critical issues
        critical_failures = [name for name, result in self.test_results.items()
                           if result['status'] == 'error']

        if critical_failures:
            print("  🔴 CRITICAL ISSUES DETECTED:")
            for failure in critical_failures:
                print(f"    - {failure.replace('_', ' ').title()} failed")
            print("    1. Check database configuration and connectivity")
            print("    2. Verify database service is running")
            print("    3. Check file permissions and disk space")
            print("    4. Review application logs for detailed errors")
        else:
            print("  ✅ No critical database issues detected")

            # Check for performance issues
            if 'performance' in self.test_results:
                perf_result = self.test_results['performance']
                if perf_result['status'] == 'warning':
                    print("  🟡 Performance optimization recommended:")
                    print("    1. Consider database indexing optimization")
                    print("    2. Review query patterns and caching")
                    print("    3. Monitor database connection pool usage")

            # Check for faculty data issues
            faculty_count = 0
            if 'faculty_operations' in self.test_results:
                faculty_result = self.test_results['faculty_operations']
                faculty_count = faculty_result.get('details', {}).get('faculty_count', 0)

            if faculty_count == 0:
                print("  🟡 No faculty data found:")
                print("    1. Add faculty records to the database")
                print("    2. Verify faculty table initialization")
                print("    3. Check data migration scripts")
            else:
                print(f"  ✅ Faculty data looks good ({faculty_count} records)")

        print()
        print("🔧 TROUBLESHOOTING COMMANDS:")
        print("  📊 Check database status: python database_diagnostics.py")
        print("  🔍 Test MQTT integration: python run_mqtt_diagnostics.py")
        print("  📡 Monitor faculty updates: python mqtt_frequency_monitor.py")
        print("=" * 80)


def main():
    """Main function to run database diagnostics."""
    print("🔍 Database Diagnostics for ConsultEase")
    print("This tool will test database connectivity, operations, and faculty data persistence")
    print()

    diagnostics = DatabaseDiagnostics()

    try:
        results = diagnostics.run_full_diagnostics()

        # Check overall health
        failed_tests = [name for name, result in results.items() if result['status'] == 'error']
        warning_tests = [name for name, result in results.items() if result['status'] == 'warning']

        if failed_tests:
            print(f"\n❌ {len(failed_tests)} critical database issues detected")
            print("🔧 Review the diagnostic report above for specific fixes")
        elif warning_tests:
            print(f"\n⚠️ {len(warning_tests)} database warnings detected")
            print("💡 Consider addressing the recommendations above")
        else:
            print("\n✅ All database tests passed successfully!")
            print("🎉 Database appears to be working correctly")

    except Exception as e:
        logger.error(f"❌ Diagnostic tool error: {e}")
        print(f"\n❌ Diagnostic tool encountered an error: {e}")
        print("📝 Check database_diagnostics.log for detailed error information")

    print("\n📝 Detailed logs saved to database_diagnostics.log")


if __name__ == "__main__":
    main()