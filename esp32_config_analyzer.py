#!/usr/bin/env python3
"""
ESP32 Configuration Analyzer for ConsultEase Faculty Desk Units
Analyzes ESP32 firmware configuration and suggests improvements for continuous MQTT updates.
"""

import os
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ESP32ConfigAnalyzer:
    """Analyze ESP32 configuration for MQTT publishing patterns."""
    
    def __init__(self):
        self.config_file = "faculty_desk_unit/config.h"
        self.main_file = "faculty_desk_unit/faculty_desk_unit.ino"
        self.issues = []
        self.recommendations = []
        
    def analyze_configuration(self):
        """Analyze ESP32 configuration for MQTT publishing issues."""
        logger.info("🔍 Analyzing ESP32 configuration for MQTT publishing patterns...")
        
        # Check if files exist
        if not os.path.exists(self.config_file):
            logger.error(f"❌ Config file not found: {self.config_file}")
            return False
            
        if not os.path.exists(self.main_file):
            logger.error(f"❌ Main file not found: {self.main_file}")
            return False
            
        # Analyze configuration
        self._analyze_config_file()
        self._analyze_main_file()
        self._generate_recommendations()
        
        # Generate report
        self._generate_analysis_report()
        
        return True
        
    def _analyze_config_file(self):
        """Analyze the config.h file for timing and MQTT settings."""
        logger.info("📋 Analyzing config.h file...")
        
        try:
            with open(self.config_file, 'r') as f:
                content = f.read()
                
            # Extract key configuration values
            configs = self._extract_config_values(content)
            
            # Analyze heartbeat interval
            heartbeat_interval = configs.get('HEARTBEAT_INTERVAL', 0)
            if heartbeat_interval >= 300000:  # 5 minutes or more
                self.issues.append({
                    'type': 'HEARTBEAT_FREQUENCY',
                    'severity': 'HIGH',
                    'description': f'Heartbeat interval is {heartbeat_interval/1000/60:.1f} minutes - too infrequent for real-time updates',
                    'current_value': heartbeat_interval,
                    'recommended_value': 30000  # 30 seconds
                })
                
            # Check for status update interval
            status_interval = configs.get('STATUS_UPDATE_INTERVAL', 0)
            if status_interval == 0:
                self.issues.append({
                    'type': 'MISSING_STATUS_INTERVAL',
                    'severity': 'HIGH', 
                    'description': 'No periodic status update interval defined',
                    'recommendation': 'Add STATUS_PUBLISH_INTERVAL for continuous status updates'
                })
            elif status_interval > 60000:  # More than 1 minute
                self.issues.append({
                    'type': 'STATUS_FREQUENCY',
                    'severity': 'MEDIUM',
                    'description': f'Status update interval is {status_interval/1000:.1f} seconds - may be too infrequent',
                    'current_value': status_interval,
                    'recommended_value': 30000  # 30 seconds
                })
                
            # Check MQTT keep-alive
            keepalive = configs.get('MQTT_KEEPALIVE', 0)
            if keepalive > 60:
                self.issues.append({
                    'type': 'MQTT_KEEPALIVE',
                    'severity': 'MEDIUM',
                    'description': f'MQTT keep-alive is {keepalive} seconds - may cause connection drops',
                    'current_value': keepalive,
                    'recommended_value': 30
                })
                
            # Check BLE scan intervals
            ble_monitoring = configs.get('BLE_SCAN_INTERVAL_MONITORING', 0)
            if ble_monitoring > 10000:  # More than 10 seconds
                self.issues.append({
                    'type': 'BLE_MONITORING_FREQUENCY',
                    'severity': 'MEDIUM',
                    'description': f'BLE monitoring interval is {ble_monitoring/1000:.1f} seconds - may miss quick presence changes',
                    'current_value': ble_monitoring,
                    'recommended_value': 5000  # 5 seconds
                })
                
            logger.info(f"✅ Config analysis complete - found {len(self.issues)} issues")
            
        except Exception as e:
            logger.error(f"❌ Error analyzing config file: {e}")
            
    def _extract_config_values(self, content):
        """Extract configuration values from config file content."""
        configs = {}
        
        # Pattern to match #define statements
        pattern = r'#define\s+(\w+)\s+(\d+)'
        matches = re.findall(pattern, content)
        
        for name, value in matches:
            try:
                configs[name] = int(value)
            except ValueError:
                pass
                
        return configs
        
    def _analyze_main_file(self):
        """Analyze the main .ino file for publishing logic."""
        logger.info("📋 Analyzing main firmware file...")
        
        try:
            with open(self.main_file, 'r') as f:
                content = f.read()
                
            # Check for periodic status publishing
            if 'publishPresenceUpdate()' not in content:
                self.issues.append({
                    'type': 'MISSING_PUBLISH_FUNCTION',
                    'severity': 'HIGH',
                    'description': 'No presence update publishing function found'
                })
            else:
                # Check where publishPresenceUpdate is called
                publish_calls = self._find_function_calls(content, 'publishPresenceUpdate')
                
                if len(publish_calls) < 2:
                    self.issues.append({
                        'type': 'LIMITED_PUBLISH_CALLS',
                        'severity': 'HIGH',
                        'description': 'publishPresenceUpdate() may only be called on status changes, not periodically',
                        'details': f'Found {len(publish_calls)} call locations'
                    })
                    
            # Check for heartbeat publishing in main loop
            if 'publishHeartbeat()' in content:
                heartbeat_calls = self._find_function_calls(content, 'publishHeartbeat')
                if not any('HEARTBEAT_INTERVAL' in call for call in heartbeat_calls):
                    self.issues.append({
                        'type': 'HEARTBEAT_LOGIC',
                        'severity': 'MEDIUM',
                        'description': 'Heartbeat publishing may not be properly scheduled'
                    })
            else:
                self.issues.append({
                    'type': 'MISSING_HEARTBEAT',
                    'severity': 'HIGH',
                    'description': 'No heartbeat publishing function found'
                })
                
            # Check for continuous status updates in main loop
            main_loop = self._extract_main_loop(content)
            if main_loop and 'publishPresenceUpdate' not in main_loop:
                self.issues.append({
                    'type': 'NO_PERIODIC_STATUS',
                    'severity': 'HIGH',
                    'description': 'Main loop does not include periodic status publishing',
                    'recommendation': 'Add periodic status updates to main loop'
                })
                
            logger.info(f"✅ Main file analysis complete")
            
        except Exception as e:
            logger.error(f"❌ Error analyzing main file: {e}")
            
    def _find_function_calls(self, content, function_name):
        """Find all calls to a specific function."""
        pattern = rf'{function_name}\s*\([^)]*\)'
        return re.findall(pattern, content)
        
    def _extract_main_loop(self, content):
        """Extract the main loop function content."""
        # Find the loop() function
        pattern = r'void\s+loop\s*\(\s*\)\s*\{(.*?)\n\}'
        match = re.search(pattern, content, re.DOTALL)
        return match.group(1) if match else None
        
    def _generate_recommendations(self):
        """Generate specific recommendations based on analysis."""
        logger.info("💡 Generating recommendations...")
        
        # High priority recommendations
        high_priority = [issue for issue in self.issues if issue['severity'] == 'HIGH']
        
        if high_priority:
            self.recommendations.extend([
                {
                    'priority': 'HIGH',
                    'title': 'Implement Continuous Status Updates',
                    'description': 'Add periodic status publishing to ensure central system stays synchronized',
                    'implementation': [
                        'Add STATUS_PUBLISH_INTERVAL constant (30-60 seconds)',
                        'Add periodic status publishing in main loop',
                        'Ensure status is published even when no changes occur'
                    ]
                },
                {
                    'priority': 'HIGH', 
                    'title': 'Reduce Heartbeat Interval',
                    'description': 'Decrease heartbeat frequency for better connection monitoring',
                    'implementation': [
                        'Change HEARTBEAT_INTERVAL from 300000ms to 30000ms (30 seconds)',
                        'This will help detect connection issues faster',
                        'Provides regular "alive" signals to central system'
                    ]
                }
            ])
            
        # Medium priority recommendations
        medium_priority = [issue for issue in self.issues if issue['severity'] == 'MEDIUM']
        
        if medium_priority:
            self.recommendations.extend([
                {
                    'priority': 'MEDIUM',
                    'title': 'Optimize MQTT Connection Settings',
                    'description': 'Improve MQTT connection stability and responsiveness',
                    'implementation': [
                        'Reduce MQTT_KEEPALIVE to 30 seconds',
                        'Add connection health monitoring',
                        'Implement automatic reconnection with backoff'
                    ]
                },
                {
                    'priority': 'MEDIUM',
                    'title': 'Enhance BLE Monitoring',
                    'description': 'Improve BLE presence detection responsiveness',
                    'implementation': [
                        'Reduce BLE_SCAN_INTERVAL_MONITORING to 5 seconds',
                        'Add more frequent scans during grace periods',
                        'Implement adaptive scanning based on presence state'
                    ]
                }
            ])
            
    def _generate_analysis_report(self):
        """Generate comprehensive analysis report."""
        print("\n" + "=" * 80)
        print("🔍 ESP32 CONFIGURATION ANALYSIS REPORT")
        print("=" * 80)
        print(f"📁 Config File: {self.config_file}")
        print(f"📁 Main File: {self.main_file}")
        print(f"🔍 Issues Found: {len(self.issues)}")
        print(f"💡 Recommendations: {len(self.recommendations)}")
        print()
        
        # Issues summary
        if self.issues:
            print("❌ ISSUES IDENTIFIED:")
            for i, issue in enumerate(self.issues, 1):
                severity_icon = "🔴" if issue['severity'] == 'HIGH' else "🟡" if issue['severity'] == 'MEDIUM' else "🟢"
                print(f"  {i}. {severity_icon} {issue['type']} ({issue['severity']})")
                print(f"     {issue['description']}")
                if 'current_value' in issue:
                    print(f"     Current: {issue['current_value']}, Recommended: {issue['recommended_value']}")
                print()
        else:
            print("✅ No configuration issues found!")
            print()
            
        # Recommendations
        if self.recommendations:
            print("💡 RECOMMENDATIONS:")
            for i, rec in enumerate(self.recommendations, 1):
                priority_icon = "🔴" if rec['priority'] == 'HIGH' else "🟡"
                print(f"  {i}. {priority_icon} {rec['title']} ({rec['priority']} PRIORITY)")
                print(f"     {rec['description']}")
                print(f"     Implementation:")
                for step in rec['implementation']:
                    print(f"       • {step}")
                print()
                
        # Specific code changes
        print("🔧 SUGGESTED CODE CHANGES:")
        print()
        
        print("1. Add to config.h:")
        print("```cpp")
        print("// Continuous status publishing")
        print("#define STATUS_PUBLISH_INTERVAL 30000        // Publish status every 30 seconds")
        print("#define HEARTBEAT_INTERVAL 30000             // Heartbeat every 30 seconds (was 300000)")
        print("#define MQTT_KEEPALIVE 30                     // MQTT keep-alive 30 seconds (was 60)")
        print("#define CONNECTION_CHECK_INTERVAL 10000       // Check connections every 10 seconds")
        print("```")
        print()
        
        print("2. Add to main loop in faculty_desk_unit.ino:")
        print("```cpp")
        print("// Continuous status publishing (add to loop())")
        print("static unsigned long lastStatusPublish = 0;")
        print("if (millis() - lastStatusPublish > STATUS_PUBLISH_INTERVAL) {")
        print("  publishPresenceUpdate();  // Publish current status regardless of changes")
        print("  lastStatusPublish = millis();")
        print("}")
        print()
        print("// More frequent heartbeat (modify existing heartbeat code)")
        print("static unsigned long lastHeartbeatTime = 0;")
        print("if (millis() - lastHeartbeatTime > HEARTBEAT_INTERVAL) {")
        print("  publishHeartbeat();")
        print("  lastHeartbeatTime = millis();")
        print("}")
        print("```")
        print()
        
        print("3. Enhanced connection monitoring:")
        print("```cpp")
        print("// Add connection health check")
        print("static unsigned long lastConnectionCheck = 0;")
        print("if (millis() - lastConnectionCheck > CONNECTION_CHECK_INTERVAL) {")
        print("  if (!mqttClient.connected()) {")
        print("    DEBUG_PRINTLN(\"⚠️ MQTT connection lost - attempting reconnect\");")
        print("    connectMQTT();")
        print("  }")
        print("  lastConnectionCheck = millis();")
        print("}")
        print("```")
        print()
        
        print("🎯 EXPECTED RESULTS AFTER CHANGES:")
        print("  ✅ Status updates every 30 seconds (instead of only on changes)")
        print("  ✅ Heartbeats every 30 seconds (instead of 5 minutes)")
        print("  ✅ Faster detection of connection issues")
        print("  ✅ Real-time dashboard updates in central system")
        print("  ✅ Better monitoring and debugging capabilities")
        print()
        
        print("⚠️ IMPORTANT NOTES:")
        print("  • More frequent publishing will increase power consumption")
        print("  • Monitor ESP32 performance and adjust intervals if needed")
        print("  • Test changes thoroughly before deploying to all units")
        print("  • Consider implementing adaptive intervals based on activity")
        
        print("=" * 80)


def main():
    """Main function to run ESP32 configuration analysis."""
    print("🔍 ESP32 Configuration Analyzer for ConsultEase")
    print("Analyzing ESP32 firmware for MQTT publishing patterns")
    print()
    
    analyzer = ESP32ConfigAnalyzer()
    success = analyzer.analyze_configuration()
    
    if not success:
        print("❌ Analysis failed - check file paths and try again")
        return
        
    print("\n📝 Analysis complete!")
    print("💡 Apply the suggested changes to enable continuous status updates")
    print("🔧 Test changes on one unit before deploying to all faculty desk units")


if __name__ == "__main__":
    main()
