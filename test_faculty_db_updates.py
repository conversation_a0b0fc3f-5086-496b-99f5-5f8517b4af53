#!/usr/bin/env python3
"""
Faculty Database Update Test Script
Tests the faculty controller's database update methods and real-time callback system.
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add the central_system directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'central_system'))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('faculty_db_test.log')
    ]
)
logger = logging.getLogger(__name__)


def test_faculty_database_updates():
    """Test faculty database update operations."""
    logger.info("🧪 Testing faculty database update operations...")
    
    try:
        from central_system.controllers.faculty_controller import FacultyController
        from central_system.models import Faculty, get_db
        
        # Create faculty controller
        faculty_controller = FacultyController()
        
        # Test 1: Get all faculty
        logger.info("📋 Test 1: Getting all faculty...")
        faculties = faculty_controller.get_all_faculty()
        logger.info(f"✅ Found {len(faculties)} faculty members")
        
        if not faculties:
            logger.error("❌ No faculty found - cannot test database updates")
            return False
            
        # Test 2: Test direct database update
        logger.info("📋 Test 2: Testing direct database update...")
        faculty = faculties[0]
        original_status = faculty.status
        original_last_seen = faculty.last_seen
        
        logger.info(f"👤 Testing with faculty: {faculty.name} (ID: {faculty.id})")
        logger.info(f"📊 Original status: {original_status}")
        logger.info(f"🕐 Original last seen: {original_last_seen}")
        
        # Update via faculty controller
        new_status = not original_status
        updated_faculty = faculty_controller.update_faculty_status(faculty.id, new_status)
        
        if updated_faculty:
            logger.info(f"✅ Faculty controller update successful")
            logger.info(f"📊 New status: {updated_faculty.status}")
            logger.info(f"🕐 New last seen: {updated_faculty.last_seen}")
            
            # Verify persistence
            db = get_db()
            db_faculty = db.query(Faculty).filter(Faculty.id == faculty.id).first()
            
            if db_faculty.status == new_status:
                logger.info("✅ Status update persisted to database")
            else:
                logger.error("❌ Status update did not persist to database")
                
            if db_faculty.last_seen > original_last_seen:
                logger.info("✅ Last seen timestamp updated")
            else:
                logger.error("❌ Last seen timestamp not updated")
                
            db.close()
            
            # Revert status
            faculty_controller.update_faculty_status(faculty.id, original_status)
            logger.info("🔄 Reverted status to original")
            
        else:
            logger.error("❌ Faculty controller update failed")
            return False
            
        # Test 3: Test MQTT message handling
        logger.info("📋 Test 3: Testing MQTT message handling...")
        
        test_data = {
            'faculty_id': faculty.id,
            'present': True,
            'status': 'AVAILABLE',
            'timestamp': int(time.time()),
            'ntp_sync_status': 'SYNCED'
        }
        
        topic = f"consultease/faculty/{faculty.id}/status"
        
        # Get current state
        db = get_db()
        before_faculty = db.query(Faculty).filter(Faculty.id == faculty.id).first()
        before_last_seen = before_faculty.last_seen
        db.close()
        
        # Handle MQTT message
        faculty_controller.handle_faculty_status_update(topic, test_data)
        
        # Check if update persisted
        time.sleep(0.1)  # Small delay for processing
        db = get_db()
        after_faculty = db.query(Faculty).filter(Faculty.id == faculty.id).first()
        
        if after_faculty.last_seen > before_last_seen:
            logger.info("✅ MQTT message handling updated database")
        else:
            logger.error("❌ MQTT message handling did not update database")
            
        if after_faculty.status == test_data['present']:
            logger.info("✅ MQTT status update persisted correctly")
        else:
            logger.error("❌ MQTT status update did not persist correctly")
            
        db.close()
        
        # Test 4: Test callback system
        logger.info("📋 Test 4: Testing callback system...")
        
        callback_called = {'value': False, 'faculty': None}
        
        def test_callback(faculty):
            callback_called['value'] = True
            callback_called['faculty'] = faculty
            logger.info(f"🔔 Callback triggered for faculty: {faculty.name if faculty else 'None'}")
            
        # Register callback
        faculty_controller.register_callback(test_callback)
        
        # Trigger update
        faculty_controller.update_faculty_status(faculty.id, not faculty.status)
        
        # Check if callback was called
        time.sleep(0.1)  # Small delay for callback processing
        
        if callback_called['value']:
            logger.info("✅ Callback system working correctly")
        else:
            logger.error("❌ Callback system not working")
            
        # Revert status
        faculty_controller.update_faculty_status(faculty.id, original_status)
        
        logger.info("✅ All faculty database update tests completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing faculty database updates: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_database_transaction_integrity():
    """Test database transaction integrity."""
    logger.info("🧪 Testing database transaction integrity...")
    
    try:
        from central_system.models import Faculty, get_db
        
        db = get_db()
        
        # Find a faculty to test with
        faculty = db.query(Faculty).first()
        if not faculty:
            logger.error("❌ No faculty found for transaction testing")
            return False
            
        original_status = faculty.status
        original_name = faculty.name
        
        logger.info(f"👤 Testing with faculty: {faculty.name} (ID: {faculty.id})")
        
        # Test successful transaction
        try:
            faculty.status = not original_status
            faculty.name = f"{original_name}_TEST"
            db.commit()
            
            # Verify changes
            db.refresh(faculty)
            if faculty.status != original_status and faculty.name.endswith("_TEST"):
                logger.info("✅ Transaction commit successful")
            else:
                logger.error("❌ Transaction commit failed")
                
        except Exception as e:
            logger.error(f"❌ Transaction commit error: {e}")
            db.rollback()
            
        # Test rollback
        try:
            faculty.status = original_status
            faculty.name = f"{original_name}_ROLLBACK"
            
            # Simulate error and rollback
            db.rollback()
            
            # Verify rollback
            db.refresh(faculty)
            if faculty.name.endswith("_TEST"):  # Should still have TEST from previous commit
                logger.info("✅ Transaction rollback successful")
            else:
                logger.error("❌ Transaction rollback failed")
                
        except Exception as e:
            logger.error(f"❌ Transaction rollback error: {e}")
            
        # Restore original state
        faculty.status = original_status
        faculty.name = original_name
        db.commit()
        
        db.close()
        
        logger.info("✅ Database transaction integrity tests completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing transaction integrity: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_concurrent_database_access():
    """Test concurrent database access."""
    logger.info("🧪 Testing concurrent database access...")
    
    try:
        from central_system.models import Faculty, get_db
        import threading
        import time
        
        # Find a faculty to test with
        db = get_db()
        faculty = db.query(Faculty).first()
        db.close()
        
        if not faculty:
            logger.error("❌ No faculty found for concurrent testing")
            return False
            
        results = {'success': 0, 'errors': 0}
        
        def concurrent_update(thread_id):
            try:
                db = get_db()
                test_faculty = db.query(Faculty).filter(Faculty.id == faculty.id).first()
                
                # Simulate some work
                time.sleep(0.1)
                
                # Update last_seen
                test_faculty.last_seen = datetime.now()
                db.commit()
                
                results['success'] += 1
                logger.info(f"✅ Thread {thread_id} completed successfully")
                
                db.close()
                
            except Exception as e:
                results['errors'] += 1
                logger.error(f"❌ Thread {thread_id} error: {e}")
                
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=concurrent_update, args=(i,))
            threads.append(thread)
            
        # Start all threads
        for thread in threads:
            thread.start()
            
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
            
        logger.info(f"📊 Concurrent access results: {results['success']} successful, {results['errors']} errors")
        
        if results['errors'] == 0:
            logger.info("✅ Concurrent database access test passed")
            return True
        else:
            logger.error("❌ Concurrent database access test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing concurrent access: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def main():
    """Main function to run all faculty database tests."""
    print("🧪 Faculty Database Update Tests for ConsultEase")
    print("Testing database operations, transactions, and real-time updates")
    print()
    
    tests = [
        ("Faculty Database Updates", test_faculty_database_updates),
        ("Database Transaction Integrity", test_database_transaction_integrity),
        ("Concurrent Database Access", test_concurrent_database_access)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"🔍 Running {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ Test {test_name} failed with exception: {e}")
            results[test_name] = False
        print()
        
    # Summary
    print("=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
            
    print()
    print(f"Total: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All database tests passed!")
        print("✅ Faculty database operations are working correctly")
    else:
        print("⚠️ Some database tests failed")
        print("🔧 Check the logs above for specific issues")
        
    print("\n📝 Detailed logs saved to faculty_db_test.log")


if __name__ == "__main__":
    main()
