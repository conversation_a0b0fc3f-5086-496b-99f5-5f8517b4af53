#!/usr/bin/env python3
"""
MQTT Faculty Status Synchronization Diagnostic Tool

This script helps diagnose MQTT message flow and database update issues
between ESP32 faculty desk units and the central Raspberry Pi system.
"""

import json
import logging
import time
import threading
from datetime import datetime
from typing import Dict, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MQTTDiagnostics:
    """Diagnostic tool for MQTT faculty status synchronization."""

    def __init__(self):
        self.message_count = 0
        self.messages_by_topic = {}
        self.faculty_status_updates = {}
        self.database_updates = {}
        self.errors = []
        self.start_time = datetime.now()

    def start_diagnostics(self, duration_minutes=5):
        """
        Start comprehensive MQTT diagnostics.

        Args:
            duration_minutes: How long to run diagnostics
        """
        logger.info(f"🔍 Starting MQTT Faculty Status Diagnostics for {duration_minutes} minutes...")

        # Test 1: Check MQTT service connectivity
        self._test_mqtt_connectivity()

        # Test 2: Check database connectivity
        self._test_database_connectivity()

        # Test 3: Check faculty records in database
        self._check_faculty_records()

        # Test 4: Monitor MQTT messages
        self._monitor_mqtt_messages(duration_minutes)

        # Test 5: Check for missing heartbeats and continuous updates
        self._check_missing_updates()

        # Test 6: Generate diagnostic report
        self._generate_report()

    def _test_mqtt_connectivity(self):
        """Test MQTT service connectivity."""
        logger.info("🔌 Testing MQTT connectivity...")

        try:
            from ..services.async_mqtt_service import get_async_mqtt_service
            from ..config import get_config

            config = get_config()
            broker_host = config.get('mqtt.broker_host', 'localhost')
            broker_port = config.get('mqtt.broker_port', 1883)

            logger.info(f"🔧 MQTT Configuration:")
            logger.info(f"  📍 Broker: {broker_host}:{broker_port}")
            logger.info(f"  🔑 Username: {config.get('mqtt.username', 'None')}")
            logger.info(f"  🔒 Password: {'Set' if config.get('mqtt.password') else 'None'}")

            mqtt_service = get_async_mqtt_service()

            if mqtt_service.is_connected:
                logger.info("✅ MQTT service is connected")
                logger.info(f"📊 MQTT Stats - Received: {mqtt_service.messages_received}, Published: {mqtt_service.messages_published}")

                # Test broker ping
                self._test_broker_ping(mqtt_service)

                # Check registered handlers
                handlers = mqtt_service.message_handlers
                logger.info(f"📡 Registered handlers: {len(handlers)}")
                for topic in handlers.keys():
                    logger.info(f"  - {topic}")

            else:
                logger.error("❌ MQTT service is not connected")
                self.errors.append("MQTT service not connected")

                # Try to diagnose connection issues
                self._diagnose_connection_issues(broker_host, broker_port)

        except Exception as e:
            logger.error(f"❌ Error testing MQTT connectivity: {e}")
            self.errors.append(f"MQTT connectivity error: {e}")

    def _test_broker_ping(self, mqtt_service):
        """Test MQTT broker responsiveness."""
        logger.info("🏓 Testing MQTT broker ping...")

        try:
            # Publish a ping message
            ping_topic = "consultease/diagnostics/ping"
            ping_data = {
                'type': 'ping',
                'timestamp': time.time(),
                'source': 'diagnostics'
            }

            mqtt_service.publish_async(ping_topic, ping_data)
            logger.info(f"✅ Ping message sent to {ping_topic}")

        except Exception as e:
            logger.error(f"❌ Error sending ping: {e}")
            self.errors.append(f"Broker ping error: {e}")

    def _diagnose_connection_issues(self, broker_host, broker_port):
        """Diagnose MQTT connection issues."""
        logger.info("🔍 Diagnosing connection issues...")

        import socket

        try:
            # Test network connectivity to broker
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((broker_host, broker_port))
            sock.close()

            if result == 0:
                logger.info(f"✅ Network connection to {broker_host}:{broker_port} is working")
            else:
                logger.error(f"❌ Cannot connect to {broker_host}:{broker_port} - Network issue")
                self.errors.append(f"Network connection failed to {broker_host}:{broker_port}")

        except Exception as e:
            logger.error(f"❌ Network test error: {e}")
            self.errors.append(f"Network test error: {e}")

    def _test_database_connectivity(self):
        """Test database connectivity."""
        logger.info("🗄️ Testing database connectivity...")

        try:
            from ..services.database_manager import get_database_manager
            db_manager = get_database_manager()

            # Test database connection
            with db_manager.get_session_context() as db:
                # Simple query to test connection
                result = db.execute("SELECT 1").fetchone()
                if result:
                    logger.info("✅ Database connection successful")
                else:
                    logger.error("❌ Database query failed")
                    self.errors.append("Database query failed")

        except Exception as e:
            logger.error(f"❌ Database connectivity error: {e}")
            self.errors.append(f"Database connectivity error: {e}")

    def _check_faculty_records(self):
        """Check faculty records in database."""
        logger.info("👥 Checking faculty records...")

        try:
            from ..models import Faculty, get_db

            db = get_db()
            faculties = db.query(Faculty).all()

            logger.info(f"📊 Found {len(faculties)} faculty records:")
            for faculty in faculties:
                logger.info(f"  - ID: {faculty.id}, Name: {faculty.name}, Status: {faculty.status}, BLE ID: {faculty.ble_id}")

            # Check for faculty ID 1 specifically (ESP32 default)
            faculty_1 = db.query(Faculty).filter(Faculty.id == 1).first()
            if faculty_1:
                logger.info(f"✅ Faculty ID 1 found: {faculty_1.name} (Status: {faculty_1.status})")
            else:
                logger.warning("⚠️ Faculty ID 1 not found - ESP32 may be configured for non-existent faculty")
                self.errors.append("Faculty ID 1 not found in database")

        except Exception as e:
            logger.error(f"❌ Error checking faculty records: {e}")
            self.errors.append(f"Faculty records error: {e}")

    def _monitor_mqtt_messages(self, duration_minutes):
        """Monitor MQTT messages for the specified duration."""
        logger.info(f"📡 Monitoring MQTT messages for {duration_minutes} minutes...")

        try:
            from ..services.async_mqtt_service import get_async_mqtt_service
            mqtt_service = get_async_mqtt_service()

            # Register diagnostic handler
            original_handlers = mqtt_service.message_handlers.copy()

            # Add our diagnostic handler
            mqtt_service.register_topic_handler("consultease/faculty/+/status", self._diagnostic_message_handler)
            mqtt_service.register_topic_handler("faculty/+/status", self._diagnostic_message_handler)
            mqtt_service.register_topic_handler("professor/status", self._diagnostic_message_handler)

            # Monitor for specified duration
            end_time = time.time() + (duration_minutes * 60)

            logger.info("🎧 Listening for MQTT messages...")
            logger.info("📝 Expected ESP32 topics:")
            logger.info("  - consultease/faculty/1/status")
            logger.info("  - faculty/1/status (legacy)")

            while time.time() < end_time:
                time.sleep(1)

            logger.info("⏰ Monitoring period completed")

        except Exception as e:
            logger.error(f"❌ Error monitoring MQTT messages: {e}")
            self.errors.append(f"MQTT monitoring error: {e}")

    def _diagnostic_message_handler(self, topic: str, data: Any):
        """Handle MQTT messages for diagnostics."""
        self.message_count += 1
        timestamp = datetime.now().isoformat()

        # Track messages by topic
        if topic not in self.messages_by_topic:
            self.messages_by_topic[topic] = []
        self.messages_by_topic[topic].append({
            'timestamp': timestamp,
            'data': data,
            'data_type': type(data).__name__
        })

        logger.info(f"📨 MQTT Message #{self.message_count}")
        logger.info(f"  📍 Topic: {topic}")
        logger.info(f"  📄 Data: {data}")
        logger.info(f"  🏷️ Type: {type(data).__name__}")

        # Analyze faculty status messages
        if 'faculty' in topic and 'status' in topic:
            self._analyze_faculty_status_message(topic, data, timestamp)

    def _analyze_faculty_status_message(self, topic: str, data: Any, timestamp: str):
        """Analyze faculty status messages."""
        logger.info("🔍 Analyzing faculty status message...")

        # Extract faculty ID from topic
        faculty_id = None
        if 'consultease/faculty/' in topic:
            parts = topic.split('/')
            if len(parts) >= 3:
                try:
                    faculty_id = int(parts[2])
                except ValueError:
                    pass
        elif topic == 'professor/status':
            faculty_id = 1  # Legacy topic typically for faculty ID 1

        logger.info(f"  👤 Faculty ID: {faculty_id}")

        # Analyze data content
        if isinstance(data, dict):
            logger.info("  📊 Data analysis:")
            for key, value in data.items():
                logger.info(f"    - {key}: {value}")

            # Check for presence indicators
            if 'present' in data:
                status = bool(data['present'])
                logger.info(f"  ✅ Found 'present' field: {status}")
            elif 'status' in data:
                status = data['status']
                logger.info(f"  📝 Found 'status' field: {status}")
            else:
                logger.warning("  ⚠️ No clear status indicator found")

        # Track this update
        if faculty_id:
            if faculty_id not in self.faculty_status_updates:
                self.faculty_status_updates[faculty_id] = []
            self.faculty_status_updates[faculty_id].append({
                'timestamp': timestamp,
                'topic': topic,
                'data': data
            })

    def _check_missing_updates(self):
        """Check for missing heartbeats and continuous status updates."""
        logger.info("💓 Checking for missing heartbeats and continuous updates...")

        current_time = time.time()

        # Expected intervals (in seconds)
        expected_heartbeat_interval = 300  # 5 minutes (current ESP32 config)
        expected_status_interval = 60     # Should be every minute for real-time updates

        # Check each faculty for missing updates
        for faculty_id, updates in self.faculty_status_updates.items():
            if not updates:
                logger.warning(f"⚠️ Faculty {faculty_id}: No status updates received")
                self.errors.append(f"Faculty {faculty_id}: No status updates")
                continue

            # Get last update time
            last_update = updates[-1]
            last_timestamp = datetime.fromisoformat(last_update['timestamp'])
            time_since_last = (datetime.now() - last_timestamp).total_seconds()

            logger.info(f"👤 Faculty {faculty_id}:")
            logger.info(f"  📊 Total updates: {len(updates)}")
            logger.info(f"  🕐 Last update: {time_since_last:.1f} seconds ago")

            # Check for missing heartbeats
            if time_since_last > expected_heartbeat_interval * 1.5:
                logger.warning(f"  ⚠️ Missing heartbeat: {time_since_last:.1f}s since last update (expected every {expected_heartbeat_interval}s)")
                self.errors.append(f"Faculty {faculty_id}: Missing heartbeat ({time_since_last:.1f}s)")

            # Check update frequency
            if len(updates) > 1:
                # Calculate average interval between updates
                intervals = []
                for i in range(1, len(updates)):
                    prev_time = datetime.fromisoformat(updates[i-1]['timestamp'])
                    curr_time = datetime.fromisoformat(updates[i]['timestamp'])
                    interval = (curr_time - prev_time).total_seconds()
                    intervals.append(interval)

                avg_interval = sum(intervals) / len(intervals)
                logger.info(f"  📈 Average update interval: {avg_interval:.1f} seconds")

                # Check if updates are too infrequent
                if avg_interval > expected_status_interval * 2:
                    logger.warning(f"  ⚠️ Infrequent updates: Average {avg_interval:.1f}s (should be ~{expected_status_interval}s for real-time)")
                    self.errors.append(f"Faculty {faculty_id}: Infrequent updates ({avg_interval:.1f}s average)")

                # Check for only-on-change updates (large gaps)
                max_interval = max(intervals)
                if max_interval > expected_heartbeat_interval:
                    logger.warning(f"  ⚠️ Large gap detected: {max_interval:.1f}s between updates")
                    logger.warning(f"  💡 This suggests ESP32 only publishes on status changes, not continuously")
                    self.errors.append(f"Faculty {faculty_id}: Large update gap ({max_interval:.1f}s) - likely only publishing on changes")

        # Overall assessment
        if not self.faculty_status_updates:
            logger.error("❌ No faculty status updates received at all")
            logger.error("💡 This indicates ESP32 units are not publishing MQTT messages")
            self.errors.append("No faculty status updates received - ESP32 units not publishing")
        else:
            total_updates = sum(len(updates) for updates in self.faculty_status_updates.values())
            monitoring_duration = 5 * 60  # 5 minutes in seconds
            updates_per_minute = total_updates / (monitoring_duration / 60)

            logger.info(f"📊 Overall update frequency: {updates_per_minute:.1f} updates per minute")

            if updates_per_minute < 0.5:  # Less than 1 update every 2 minutes
                logger.warning("⚠️ Very low update frequency detected")
                logger.warning("💡 ESP32 units may only be publishing on status changes")
                logger.warning("💡 Consider implementing continuous status publishing every 30-60 seconds")
                self.errors.append("Low update frequency - implement continuous status publishing")

    def _generate_report(self):
        """Generate comprehensive diagnostic report."""
        logger.info("📋 Generating diagnostic report...")

        duration = datetime.now() - self.start_time

        print("\n" + "="*80)
        print("🔍 MQTT FACULTY STATUS SYNCHRONIZATION DIAGNOSTIC REPORT")
        print("="*80)
        print(f"📅 Report generated: {datetime.now().isoformat()}")
        print(f"⏱️ Monitoring duration: {duration}")
        print(f"📨 Total messages received: {self.message_count}")
        print()

        # Messages by topic
        print("📡 MESSAGES BY TOPIC:")
        for topic, messages in self.messages_by_topic.items():
            print(f"  📍 {topic}: {len(messages)} messages")
            if messages:
                latest = messages[-1]
                print(f"    🕐 Latest: {latest['timestamp']}")
                print(f"    📄 Data: {latest['data']}")
        print()

        # Faculty status updates
        print("👥 FACULTY STATUS UPDATES:")
        if self.faculty_status_updates:
            for faculty_id, updates in self.faculty_status_updates.items():
                print(f"  👤 Faculty {faculty_id}: {len(updates)} updates")
                for update in updates[-3:]:  # Show last 3 updates
                    print(f"    🕐 {update['timestamp']}: {update['data']}")
        else:
            print("  ⚠️ No faculty status updates received")
        print()

        # Errors
        if self.errors:
            print("❌ ERRORS DETECTED:")
            for error in self.errors:
                print(f"  - {error}")
        else:
            print("✅ No errors detected")
        print()

        # Recommendations
        print("💡 RECOMMENDATIONS:")
        if self.message_count == 0:
            print("  🔴 CRITICAL: No MQTT messages received")
            print("    1. Check ESP32 MQTT connection and publishing")
            print("    2. Verify MQTT broker is running and accessible")
            print("    3. Check ESP32 configuration (MQTT_SERVER, topics)")
            print("    4. Run: python check_mqtt_broker.py")
            print("    5. Run: python mqtt_investigation.py")
        elif not self.faculty_status_updates:
            print("  🟡 WARNING: MQTT connected but no faculty status updates")
            print("    1. Verify ESP32 is publishing to correct topics")
            print("    2. Check faculty ID configuration in ESP32")
            print("    3. Verify message format matches expected structure")
            print("    4. Check ESP32 serial output for publishing logs")
        else:
            print("  ✅ MQTT messages are being received")

            # Check for frequency issues
            total_updates = sum(len(updates) for updates in self.faculty_status_updates.values())
            monitoring_duration = 5 * 60  # 5 minutes
            updates_per_minute = total_updates / (monitoring_duration / 60)

            if updates_per_minute < 0.5:
                print("  🟡 WARNING: Low update frequency detected")
                print("    💡 ESP32 units appear to only publish on status changes")
                print("    💡 For real-time updates, implement continuous publishing:")
                print("    1. Add STATUS_PUBLISH_INTERVAL to ESP32 config (30-60 seconds)")
                print("    2. Reduce HEARTBEAT_INTERVAL from 300s to 30s")
                print("    3. Add periodic status publishing in main loop")
                print("    4. Run: python esp32_config_analyzer.py for detailed suggestions")
            else:
                print("  ✅ Good update frequency detected")

            # Check for missing heartbeats
            heartbeat_issues = [error for error in self.errors if 'heartbeat' in error.lower()]
            if heartbeat_issues:
                print("  🟡 WARNING: Missing heartbeats detected")
                print("    1. Check ESP32 network connectivity")
                print("    2. Verify MQTT keep-alive settings")
                print("    3. Consider reducing heartbeat interval")

        print()
        print("🔧 TROUBLESHOOTING TOOLS:")
        print("  📊 Monitor message frequency: python mqtt_frequency_monitor.py")
        print("  🔍 Analyze ESP32 config: python esp32_config_analyzer.py")
        print("  🔧 Check MQTT broker: python check_mqtt_broker.py")
        print("  📡 Test MQTT communication: python mqtt_investigation.py")

        print("="*80)


def run_diagnostics(duration_minutes=5):
    """Run MQTT diagnostics for the specified duration."""
    diagnostics = MQTTDiagnostics()
    diagnostics.start_diagnostics(duration_minutes)


if __name__ == "__main__":
    # Run diagnostics for 5 minutes
    run_diagnostics(5)
