#!/usr/bin/env python3
"""
MQTT Message Frequency Monitor for ConsultEase
Monitors MQTT message frequency from ESP32 faculty desk units to identify publishing patterns.
"""

import sys
import time
import json
import logging
import threading
from datetime import datetime, timedelta
from collections import defaultdict, deque
import paho.mqtt.client as mqtt

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('mqtt_frequency_monitor.log')
    ]
)
logger = logging.getLogger(__name__)


class MQTTFrequencyMonitor:
    """Monitor MQTT message frequency and patterns from ESP32 faculty desk units."""
    
    def __init__(self, broker_host='localhost', broker_port=1883):
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.client = None
        self.monitoring = False
        
        # Message tracking
        self.message_history = defaultdict(deque)  # topic -> deque of timestamps
        self.last_message_time = defaultdict(float)  # topic -> last message timestamp
        self.message_counts = defaultdict(int)  # topic -> total message count
        self.faculty_status = {}  # faculty_id -> last status
        
        # Frequency analysis
        self.expected_heartbeat_interval = 300  # 5 minutes in seconds
        self.expected_status_interval = 60     # Expected status updates every minute
        self.monitoring_duration = 600         # Monitor for 10 minutes by default
        
        # Statistics
        self.stats = {
            'total_messages': 0,
            'heartbeat_messages': 0,
            'status_messages': 0,
            'other_messages': 0,
            'faculty_units_seen': set(),
            'monitoring_start': None,
            'monitoring_end': None
        }
        
    def start_monitoring(self, duration_minutes=10):
        """Start monitoring MQTT messages for the specified duration."""
        logger.info(f"🔍 Starting MQTT frequency monitoring for {duration_minutes} minutes...")
        
        self.monitoring_duration = duration_minutes * 60
        self.stats['monitoring_start'] = time.time()
        
        try:
            # Set up MQTT client
            self.client = mqtt.Client("mqtt_frequency_monitor")
            self.client.on_connect = self._on_connect
            self.client.on_message = self._on_message
            self.client.on_disconnect = self._on_disconnect
            
            # Connect to broker
            self.client.connect(self.broker_host, self.broker_port, 60)
            self.client.loop_start()
            
            # Monitor for specified duration
            self.monitoring = True
            start_time = time.time()
            
            while self.monitoring and (time.time() - start_time) < self.monitoring_duration:
                time.sleep(1)
                
                # Print periodic updates
                if int(time.time() - start_time) % 60 == 0:  # Every minute
                    self._print_periodic_update(time.time() - start_time)
                    
            self.monitoring = False
            self.stats['monitoring_end'] = time.time()
            
            # Generate final report
            self._generate_frequency_report()
            
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring interrupted by user")
            self.monitoring = False
        except Exception as e:
            logger.error(f"❌ Error during monitoring: {e}")
        finally:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()
                
    def _on_connect(self, client, userdata, flags, rc):
        """MQTT connection callback."""
        if rc == 0:
            logger.info(f"✅ Connected to MQTT broker at {self.broker_host}:{self.broker_port}")
            
            # Subscribe to all faculty topics
            topics = [
                "consultease/faculty/+/status",
                "consultease/faculty/+/heartbeat", 
                "consultease/faculty/+/messages",
                "consultease/faculty/+/responses",
                "faculty/+/status",
                "faculty/+/heartbeat",
                "professor/status",
                "professor/messages"
            ]
            
            for topic in topics:
                client.subscribe(topic, 1)
                logger.info(f"📡 Subscribed to {topic}")
                
        else:
            logger.error(f"❌ Failed to connect to MQTT broker: {rc}")
            
    def _on_disconnect(self, client, userdata, rc):
        """MQTT disconnection callback."""
        logger.info(f"🔌 Disconnected from MQTT broker: {rc}")
        
    def _on_message(self, client, userdata, msg):
        """MQTT message callback - analyze message frequency."""
        try:
            current_time = time.time()
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            # Update statistics
            self.stats['total_messages'] += 1
            self.message_counts[topic] += 1
            
            # Store message timestamp
            self.message_history[topic].append(current_time)
            self.last_message_time[topic] = current_time
            
            # Keep only recent messages (last hour)
            cutoff_time = current_time - 3600
            while self.message_history[topic] and self.message_history[topic][0] < cutoff_time:
                self.message_history[topic].popleft()
                
            # Categorize message type
            if 'heartbeat' in topic:
                self.stats['heartbeat_messages'] += 1
                message_type = "HEARTBEAT"
            elif 'status' in topic:
                self.stats['status_messages'] += 1
                message_type = "STATUS"
            else:
                self.stats['other_messages'] += 1
                message_type = "OTHER"
                
            # Extract faculty ID if possible
            faculty_id = self._extract_faculty_id(topic, payload)
            if faculty_id:
                self.stats['faculty_units_seen'].add(faculty_id)
                
            # Calculate time since last message on this topic
            time_since_last = self._calculate_time_since_last(topic, current_time)
            
            # Log message with frequency analysis
            logger.info(f"📨 {message_type} | Topic: {topic}")
            logger.info(f"    Faculty ID: {faculty_id or 'Unknown'}")
            logger.info(f"    Time since last: {time_since_last:.1f}s")
            logger.info(f"    Message count: {self.message_counts[topic]}")
            
            # Parse and analyze payload
            self._analyze_message_content(topic, payload, faculty_id)
            
            # Check for frequency issues
            self._check_frequency_issues(topic, message_type, time_since_last)
            
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
            
    def _extract_faculty_id(self, topic, payload):
        """Extract faculty ID from topic or payload."""
        # Try to extract from topic first
        if '/faculty/' in topic:
            parts = topic.split('/')
            for i, part in enumerate(parts):
                if part == 'faculty' and i + 1 < len(parts):
                    try:
                        return int(parts[i + 1])
                    except ValueError:
                        pass
                        
        # Try to extract from payload
        try:
            data = json.loads(payload)
            if 'faculty_id' in data:
                return data['faculty_id']
        except json.JSONDecodeError:
            pass
            
        return None
        
    def _calculate_time_since_last(self, topic, current_time):
        """Calculate time since last message on this topic."""
        if len(self.message_history[topic]) > 1:
            return current_time - self.message_history[topic][-2]
        return 0
        
    def _analyze_message_content(self, topic, payload, faculty_id):
        """Analyze message content for status changes and patterns."""
        try:
            data = json.loads(payload)
            
            if faculty_id and 'present' in data:
                old_status = self.faculty_status.get(faculty_id, {})
                new_present = data.get('present', False)
                old_present = old_status.get('present', None)
                
                if old_present is not None and old_present != new_present:
                    logger.info(f"🔄 Faculty {faculty_id} status changed: {old_present} → {new_present}")
                    
                # Update stored status
                self.faculty_status[faculty_id] = {
                    'present': new_present,
                    'timestamp': time.time(),
                    'status': data.get('status', 'unknown'),
                    'detailed_status': data.get('detailed_status', ''),
                    'in_grace_period': data.get('in_grace_period', False)
                }
                
                # Log grace period information
                if data.get('in_grace_period'):
                    remaining = data.get('grace_period_remaining', 0)
                    logger.info(f"⏳ Faculty {faculty_id} in grace period: {remaining}ms remaining")
                    
        except json.JSONDecodeError:
            logger.debug(f"Non-JSON payload: {payload}")
            
    def _check_frequency_issues(self, topic, message_type, time_since_last):
        """Check for frequency-related issues."""
        if message_type == "HEARTBEAT":
            if time_since_last > self.expected_heartbeat_interval * 1.5:
                logger.warning(f"⚠️ Heartbeat frequency issue: {time_since_last:.1f}s since last (expected ~{self.expected_heartbeat_interval}s)")
        elif message_type == "STATUS":
            if time_since_last > self.expected_status_interval * 2:
                logger.warning(f"⚠️ Status update frequency issue: {time_since_last:.1f}s since last")
                
    def _print_periodic_update(self, elapsed_time):
        """Print periodic monitoring update."""
        logger.info(f"📊 Monitoring Update ({elapsed_time/60:.1f} minutes elapsed)")
        logger.info(f"    Total messages: {self.stats['total_messages']}")
        logger.info(f"    Faculty units seen: {len(self.stats['faculty_units_seen'])}")
        logger.info(f"    Heartbeats: {self.stats['heartbeat_messages']}")
        logger.info(f"    Status updates: {self.stats['status_messages']}")
        
    def _generate_frequency_report(self):
        """Generate comprehensive frequency analysis report."""
        logger.info("📋 Generating MQTT Frequency Analysis Report...")
        
        duration = self.stats['monitoring_end'] - self.stats['monitoring_start']
        
        print("\n" + "=" * 80)
        print("📊 MQTT MESSAGE FREQUENCY ANALYSIS REPORT")
        print("=" * 80)
        print(f"📅 Monitoring Period: {duration/60:.1f} minutes")
        print(f"🔢 Total Messages: {self.stats['total_messages']}")
        print(f"👥 Faculty Units Detected: {len(self.stats['faculty_units_seen'])}")
        print(f"💓 Heartbeat Messages: {self.stats['heartbeat_messages']}")
        print(f"📊 Status Messages: {self.stats['status_messages']}")
        print(f"📨 Other Messages: {self.stats['other_messages']}")
        print()
        
        # Message frequency analysis
        print("📈 MESSAGE FREQUENCY ANALYSIS:")
        for topic, timestamps in self.message_history.items():
            if len(timestamps) > 1:
                intervals = []
                for i in range(1, len(timestamps)):
                    intervals.append(timestamps[i] - timestamps[i-1])
                    
                avg_interval = sum(intervals) / len(intervals)
                min_interval = min(intervals)
                max_interval = max(intervals)
                
                print(f"  📡 {topic}:")
                print(f"      Messages: {len(timestamps)}")
                print(f"      Avg Interval: {avg_interval:.1f}s")
                print(f"      Min Interval: {min_interval:.1f}s")
                print(f"      Max Interval: {max_interval:.1f}s")
                
                # Check if frequency meets expectations
                if 'heartbeat' in topic:
                    expected = self.expected_heartbeat_interval
                    if avg_interval > expected * 1.5:
                        print(f"      ⚠️ WARNING: Heartbeat too infrequent (expected ~{expected}s)")
                elif 'status' in topic:
                    if avg_interval > 120:  # More than 2 minutes
                        print(f"      ⚠️ WARNING: Status updates infrequent")
                        
        print()
        
        # Faculty status summary
        print("👥 FACULTY STATUS SUMMARY:")
        for faculty_id, status in self.faculty_status.items():
            last_seen = time.time() - status['timestamp']
            print(f"  Faculty {faculty_id}:")
            print(f"    Status: {status['status']}")
            print(f"    Present: {status['present']}")
            print(f"    Last seen: {last_seen:.1f}s ago")
            if status['in_grace_period']:
                print(f"    ⏳ In grace period")
        print()
        
        # Recommendations
        print("💡 RECOMMENDATIONS:")
        
        if self.stats['total_messages'] == 0:
            print("  ❌ NO MESSAGES RECEIVED")
            print("    1. Check if ESP32 units are powered on")
            print("    2. Verify ESP32 MQTT configuration")
            print("    3. Check network connectivity")
            print("    4. Verify MQTT broker is running")
            
        elif self.stats['heartbeat_messages'] == 0:
            print("  ⚠️ NO HEARTBEAT MESSAGES")
            print("    1. ESP32 units may not be sending periodic heartbeats")
            print("    2. Check HEARTBEAT_INTERVAL in ESP32 config (currently 5 minutes)")
            print("    3. Consider reducing heartbeat interval for better monitoring")
            
        elif self.stats['status_messages'] == 0:
            print("  ⚠️ NO STATUS MESSAGES")
            print("    1. ESP32 units may only send status on changes")
            print("    2. Consider implementing periodic status updates")
            print("    3. Check BLE detection and presence logic")
            
        else:
            print("  ✅ Messages are being received")
            
            # Check message frequency
            avg_msg_per_minute = self.stats['total_messages'] / (duration / 60)
            if avg_msg_per_minute < 0.5:  # Less than 1 message every 2 minutes
                print("    ⚠️ Message frequency is low")
                print("    1. Consider reducing heartbeat interval from 5 minutes")
                print("    2. Implement periodic status updates (every 30-60 seconds)")
                print("    3. Add connection keep-alive messages")
                
        print("=" * 80)


def main():
    """Main function to run MQTT frequency monitoring."""
    print("📊 MQTT Message Frequency Monitor for ConsultEase")
    print("This tool monitors MQTT message patterns from ESP32 faculty desk units")
    print()
    
    # Get monitoring duration from user
    try:
        duration = input("Enter monitoring duration in minutes (default 10): ").strip()
        duration = int(duration) if duration else 10
    except ValueError:
        duration = 10
        
    print(f"🔍 Monitoring MQTT messages for {duration} minutes...")
    print("Press Ctrl+C to stop monitoring early")
    print()
    
    monitor = MQTTFrequencyMonitor()
    monitor.start_monitoring(duration)
    
    print("\n📝 Detailed logs saved to mqtt_frequency_monitor.log")


if __name__ == "__main__":
    main()
